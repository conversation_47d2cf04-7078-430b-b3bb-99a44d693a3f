// ignore_for_file: deprecated_member_use

import 'package:digital_onboarding/digital_onboarding.dart';
import 'package:example/view_model/extra_documents/extra_documents_controller.dart';
import 'package:flutter/cupertino.dart';

import '../../../resources/exports/index.dart';

class DocumentTypeDropDown extends GetView<ExtraDocumentsController> {
  const DocumentTypeDropDown({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (controller.isLoading.value) {
      return const Center(
        child: CupertinoActivityIndicator(),
      );
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            children: [
              SvgPicture.asset("assets/images/extra_documents_illus.svg"),
              const SpaceH24(),
              const Text(
                "Add Documents",
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                "Choose type of document",
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.w700),
              ),
              const SpaceH8(),
              GetBuilder<ExtraDocumentsController>(
                id: 'multi_choice_dropdown',
                builder: (_) {
                  return DropdownButtonHideUnderline(
                    child: DropdownButton2<DigitalOnBoardingDocumentTypeModel>(
                      isExpanded: true,
                      hint: Text(
                        "Select Document Type",
                        style: context.titleMedium.copyWith(
                          color: AppColors.greyShade2,
                        ),
                      ),
                      items: controller.documentsTypes
                              ?.map(
                                (DigitalOnBoardingDocumentTypeModel? item) => DropdownMenuItem<DigitalOnBoardingDocumentTypeModel>(
                                  value: item,
                                  child: Text(
                                    item?.documentType ?? "",
                                    style: context.titleMedium,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              )
                              .toList() ??
                          [],
                      value: controller.selectedItem,
                      onChanged: (v) {
                        controller.toggleMultiChoiceAnswer(v);
                      },
                      buttonStyleData: ButtonStyleData(
                        height: 50,
                        width: double.maxFinite,
                        padding: const EdgeInsets.only(left: 14, right: 14),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: const Color(0xffE8E0EB),
                        ),
                        elevation: 0,
                      ),
                      iconStyleData: const IconStyleData(
                        icon: Icon(CupertinoIcons.chevron_down),
                        openMenuIcon: Icon(CupertinoIcons.chevron_up),
                        iconSize: 14,
                        iconEnabledColor: AppColors.black,
                        iconDisabledColor: Colors.grey,
                      ),
                      dropdownStyleData: DropdownStyleData(
                        maxHeight: 200,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: const Color(0xffF5FAFD),
                          border: Border.all(color: const Color(0xffE8E0EB)),
                        ),
                        offset: const Offset(0, -12),
                        elevation: 0,
                      ),
                      menuItemStyleData: const MenuItemStyleData(
                        height: 40,
                        padding: EdgeInsets.symmetric(horizontal: 14),
                      ),
                      enableFeedback: true,
                    ),
                  );
                },
              ),
            ],
          ),
          CustomButton.solid(
            backgroundColor: AppColors.primary,
            textColor: AppColors.white,
            text: "Next",
            onTap: () {
              controller.pickImage();
            },
            radius: Sizes.RADIUS_12,
            constraints: const BoxConstraints(minHeight: 55),
          ),
        ],
      ),
    );
  }
}
